<!--
  主题创建器组件
  @description 允许用户创建自定义主题
  <AUTHOR>
  @date 2024-12-12
  @version 1.0.0
-->

<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建自定义主题"
    width="600px"
    :before-close="handleClose"
    class="theme-creator-dialog"
  >
    <el-form
      ref="formRef"
      :model="themeForm"
      :rules="formRules"
      label-width="120px"
      class="theme-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>

        <el-form-item label="主题名称" prop="name">
          <el-input
            v-model="themeForm.name"
            placeholder="请输入主题名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="显示名称" prop="displayName">
          <el-input
            v-model="themeForm.displayName"
            placeholder="请输入显示名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="主题类型" prop="isDark">
          <el-radio-group v-model="themeForm.isDark">
            <el-radio :label="false">浅色主题</el-radio>
            <el-radio :label="true">深色主题</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 颜色配置 -->
      <div class="form-section">
        <h4 class="section-title">颜色配置</h4>

        <!-- 主色调 -->
        <div class="color-group">
          <h5 class="group-title">主色调</h5>
          <div class="color-row">
            <el-form-item label="主色" prop="colors.primary">
              <el-color-picker v-model="themeForm.colors.primary" />
            </el-form-item>
            <el-form-item label="主色浅" prop="colors.primaryLight">
              <el-color-picker v-model="themeForm.colors.primaryLight" />
            </el-form-item>
            <el-form-item label="主色深" prop="colors.primaryDark">
              <el-color-picker v-model="themeForm.colors.primaryDark" />
            </el-form-item>
          </div>
        </div>

        <!-- 状态色 -->
        <div class="color-group">
          <h5 class="group-title">状态色</h5>
          <div class="color-row">
            <el-form-item label="成功色" prop="colors.success">
              <el-color-picker v-model="themeForm.colors.success" />
            </el-form-item>
            <el-form-item label="警告色" prop="colors.warning">
              <el-color-picker v-model="themeForm.colors.warning" />
            </el-form-item>
            <el-form-item label="错误色" prop="colors.error">
              <el-color-picker v-model="themeForm.colors.error" />
            </el-form-item>
            <el-form-item label="信息色" prop="colors.info">
              <el-color-picker v-model="themeForm.colors.info" />
            </el-form-item>
          </div>
        </div>

        <!-- 背景色 -->
        <div class="color-group">
          <h5 class="group-title">背景色</h5>
          <div class="color-row">
            <el-form-item label="主背景" prop="colors.background">
              <el-color-picker v-model="themeForm.colors.background" />
            </el-form-item>
            <el-form-item label="次背景" prop="colors.backgroundSecondary">
              <el-color-picker v-model="themeForm.colors.backgroundSecondary" />
            </el-form-item>
            <el-form-item label="三级背景" prop="colors.backgroundTertiary">
              <el-color-picker v-model="themeForm.colors.backgroundTertiary" />
            </el-form-item>
          </div>
        </div>

        <!-- 文字色 -->
        <div class="color-group">
          <h5 class="group-title">文字色</h5>
          <div class="color-row">
            <el-form-item label="主文字" prop="colors.textPrimary">
              <el-color-picker v-model="themeForm.colors.textPrimary" />
            </el-form-item>
            <el-form-item label="次文字" prop="colors.textSecondary">
              <el-color-picker v-model="themeForm.colors.textSecondary" />
            </el-form-item>
            <el-form-item label="三级文字" prop="colors.textTertiary">
              <el-color-picker v-model="themeForm.colors.textTertiary" />
            </el-form-item>
            <el-form-item label="禁用文字" prop="colors.textDisabled">
              <el-color-picker v-model="themeForm.colors.textDisabled" />
            </el-form-item>
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="form-section">
        <h4 class="section-title">主题预览</h4>
        <div class="theme-preview" :style="previewStyle">
          <div class="preview-card">
            <div class="preview-header">
              <h5>预览卡片</h5>
              <el-button type="primary" size="small">主要按钮</el-button>
            </div>
            <div class="preview-content">
              <p class="primary-text">这是主要文字颜色</p>
              <p class="secondary-text">这是次要文字颜色</p>
              <p class="tertiary-text">这是三级文字颜色</p>
            </div>
            <div class="preview-actions">
              <el-button type="success" size="small">成功</el-button>
              <el-button type="warning" size="small">警告</el-button>
              <el-button type="danger" size="small">错误</el-button>
              <el-button type="info" size="small">信息</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 创建主题 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { useTheme } from '@/composables/useTheme'
  import { lightTheme, darkTheme, type Theme, type ThemeColors } from '@/config/theme'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

  interface Props {
    modelValue: boolean
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'theme-created', themeName: string): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 使用主题管理
  const { addCustomTheme, availableThemes } = useTheme()

  // 对话框显示状态
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 表单引用
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 主题表单数据
  const themeForm = ref<{
    name: string
    displayName: string
    isDark: boolean
    colors: ThemeColors
  }>({
    name: '',
    displayName: '',
    isDark: false,
    colors: { ...lightTheme.colors }
  })

  // 表单验证规则
  const formRules: FormRules = {
    name: [
      { required: true, message: '请输入主题名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
      {
        pattern: /^[a-zA-Z0-9_-]+$/,
        message: '只能包含字母、数字、下划线和连字符',
        trigger: 'blur'
      },
      {
        validator: (rule, value, callback) => {
          if (availableThemes.value[value]) {
            callback(new Error('主题名称已存在'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    displayName: [
      { required: true, message: '请输入显示名称', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ]
  }

  // 预览样式
  const previewStyle = computed(() => ({
    '--preview-primary': themeForm.value.colors.primary,
    '--preview-success': themeForm.value.colors.success,
    '--preview-warning': themeForm.value.colors.warning,
    '--preview-error': themeForm.value.colors.error,
    '--preview-info': themeForm.value.colors.info,
    '--preview-bg': themeForm.value.colors.background,
    '--preview-bg-secondary': themeForm.value.colors.backgroundSecondary,
    '--preview-text-primary': themeForm.value.colors.textPrimary,
    '--preview-text-secondary': themeForm.value.colors.textSecondary,
    '--preview-text-tertiary': themeForm.value.colors.textTertiary,
    '--preview-border': themeForm.value.colors.border
  }))

  // 监听主题类型变化，自动切换颜色模板
  watch(
    () => themeForm.value.isDark,
    isDark => {
      const template = isDark ? darkTheme : lightTheme
      themeForm.value.colors = { ...template.colors }
    }
  )

  /**
   * 重置表单
   */
  const resetForm = () => {
    themeForm.value = {
      name: '',
      displayName: '',
      isDark: false,
      colors: { ...lightTheme.colors }
    }
    formRef.value?.clearValidate()
  }

  /**
   * 处理提交
   */
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 创建主题对象
      const newTheme: Theme = {
        name: themeForm.value.name,
        displayName: themeForm.value.displayName,
        isDark: themeForm.value.isDark,
        colors: { ...themeForm.value.colors }
      }

      // 添加自定义主题
      addCustomTheme(newTheme)

      // 发送事件
      emit('theme-created', newTheme.name)

      // 关闭对话框
      dialogVisible.value = false

      // 重置表单
      resetForm()

      ElMessage.success('主题创建成功')
    } catch (error) {
      console.error('主题创建失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = () => {
    dialogVisible.value = false
    resetForm()
  }
</script>

<style scoped>
  .theme-creator-dialog {
    --el-dialog-content-font-size: 14px;
  }

  .theme-form {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 8px;
  }

  .form-section {
    margin-bottom: 24px;
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 8px;
  }

  .color-group {
    margin-bottom: 16px;
  }

  .group-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text-secondary);
  }

  .color-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }

  .color-row .el-form-item {
    margin-bottom: 8px;
  }

  .theme-preview {
    padding: 16px;
    background: var(--preview-bg);
    border: 1px solid var(--preview-border);
    border-radius: 8px;
  }

  .preview-card {
    background: var(--preview-bg-secondary);
    border: 1px solid var(--preview-border);
    border-radius: 6px;
    padding: 16px;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .preview-header h5 {
    margin: 0;
    color: var(--preview-text-primary);
  }

  .preview-content {
    margin-bottom: 16px;
  }

  .primary-text {
    color: var(--preview-text-primary);
    margin: 0 0 8px 0;
  }

  .secondary-text {
    color: var(--preview-text-secondary);
    margin: 0 0 8px 0;
  }

  .tertiary-text {
    color: var(--preview-text-tertiary);
    margin: 0;
  }

  .preview-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  /* 滚动条样式 */
  .theme-form::-webkit-scrollbar {
    width: 6px;
  }

  .theme-form::-webkit-scrollbar-track {
    background: var(--color-background-tertiary);
    border-radius: 3px;
  }

  .theme-form::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: 3px;
  }

  .theme-form::-webkit-scrollbar-thumb:hover {
    background: var(--color-border-dark);
  }
</style>
