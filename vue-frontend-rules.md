# Vue3 前端开发规范

## 技术栈要求

- **框架**: Vue 3 + TypeScript + Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **UI库**: Element Plus / Ant Design Vue
- **CSS**: SCSS + BEM命名
- **代码规范**: ESLint + Prettier
- **包管理**: npm

### 开发哲学
1. **类型安全优先** - 所有代码必须有完整的TypeScript类型定义
2. **组合式API优先** - 使用Composition API而非Options API
3. **模块化设计** - 按功能模块组织代码结构
4. **性能优化** - 代码分割、懒加载、缓存优化
5. **可维护性** - 清晰的命名、完整的注释、统一的风格

->

### 开发哲学
1. **类型安全优先** - 所有代码必须有完整的TypeScript类型定义
2. **组合式API优先** - 使用Composition API而非Options API
3. **模块化设计** - 按功能模块组织代码结构
4. **性能优化** - 代码分割、懒加载、缓存优化
5. **可维护性** - 清晰的命名、完整的注释、统一的风格

## 中文注释规范

### 注释原则
- **必须使用中文注释** - 所有注释必须使用简体中文
- **注释要简洁明了** - 避免冗长的描述，直接说明用途和功能
- **关键逻辑必须注释** - 复杂的业务逻辑、算法、正则表达式等必须添加注释
- **接口和类型必须注释** - 所有公共接口、类型定义都要有JSDoc注释

### 文件头注释
```typescript
/**
 * 用户管理相关API接口
 * @description 包含用户的增删改查、权限管理等功能
 * <AUTHOR>
 * @date 2024-01-15
 * @version 1.0.0
 */
```

### 函数注释
```typescript
/**
 * 获取用户列表
 * @param query 查询参数
 * @param query.page 页码，从1开始
 * @param query.pageSize 每页数量，默认10
 * @param query.keyword 搜索关键词，可选
 * @returns 返回用户列表和分页信息
 * @throws 当参数无效时抛出错误
 * @example
 * ```typescript
 * const result = await getUserList({ page: 1, pageSize: 20 })
 * console.log(result.data) // 用户列表
 * ```
 */
async function getUserList(query: UserListQuery): Promise<ApiResponse<UserListResult>> {
  // 参数验证
  if (query.page < 1) {
    throw new Error('页码必须大于0')
  }
  
  // 发送请求
  return request.get('/api/users', { params: query })
}
```

### 接口和类型注释
```typescript
/**
 * 用户信息接口
 */
export interface User {
  /** 用户ID */
  id: number
  /** 用户名 */
  name: string
  /** 邮箱地址 */
  email: string
  /** 头像URL，可选 */
  avatar?: string
  /** 用户角色 */
  role: UserRole
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 用户角色枚举
 * - admin: 管理员，拥有所有权限
 * - user: 普通用户，基础权限
 * - guest: 访客，只读权限
 */
export type UserRole = 'admin' | 'user' | 'guest'
```

### 组件注释
```vue
<template>
  <!-- 用户资料卡片组件 -->
  <div class="user-profile">
    <!-- 头部信息区域 -->
    <header class="user-profile__header">
      <h1>{{ user.name }}</h1>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="user-profile__content">
      <!-- 用户详细信息 -->
      <div class="user-info">
        <p>邮箱：{{ user.email }}</p>
        <p>角色：{{ roleText }}</p>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
/**
 * 用户资料组件
 * @description 展示用户的基本信息和操作按钮
 */

// 组件属性定义
interface Props {
  /** 用户ID */
  userId: number
  /** 是否显示操作按钮 */
  showActions?: boolean
}

// 响应式数据
const loading = ref(false) // 加载状态
const user = ref<User | null>(null) // 用户信息

// 计算属性 - 角色显示文本
const roleText = computed(() => {
  const roleMap = {
    admin: '管理员',
    user: '普通用户',
    guest: '访客'
  }
  return user.value ? roleMap[user.value.role] : '未知'
})

/**
 * 获取用户信息
 * @description 根据用户ID获取用户详细信息
 */
const fetchUser = async () => {
  try {
    loading.value = true
    // 调用API获取用户信息
    const data = await userApi.getById(props.userId)
    user.value = data
  } catch (error) {
    // 错误处理
    ElMessage.error('获取用户信息失败')
    console.error('获取用户失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 复杂逻辑注释
```typescript
/**
 * 权限检查函数
 * @param userRole 用户角色
 * @param requiredPermission 需要的权限
 * @returns 是否有权限
 */
function checkPermission(userRole: UserRole, requiredPermission: string): boolean {
  // 权限级别映射表
  const roleLevel = {
    guest: 1,   // 访客权限最低
    user: 2,    // 普通用户
    admin: 3    // 管理员权限最高
  }
  
  // 权限要求映射表
  const permissionLevel = {
    'read': 1,      // 读取权限
    'write': 2,     // 写入权限
    'delete': 3,    // 删除权限
    'admin': 3      // 管理权限
  }
  
  // 检查用户角色级别是否满足权限要求
  return roleLevel[userRole] >= (permissionLevel[requiredPermission] || 0)
}
```

### 常量和配置注释
```typescript
/**
 * API配置常量
 */
export const API_CONFIG = {
  /** API基础URL */
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  /** 请求超时时间（毫秒） */
  TIMEOUT: 10000,
  /** 重试次数 */
  RETRY_COUNT: 3
} as const

/**
 * 用户状态枚举
 */
export const USER_STATUS = {
  /** 激活状态 */
  ACTIVE: 1,
  /** 禁用状态 */
  DISABLED: 0,
  /** 待审核状态 */
  PENDING: 2
} as const
```

### 注释最佳实践
1. **避免无意义注释** - 不要为显而易见的代码添加注释
2. **及时更新注释** - 代码修改时同步更新相关注释
3. **使用TODO标记** - 临时代码或待完善功能使用TODO标记
4. **统一注释风格** - 团队内保持注释格式一致

```typescript
// ❌ 不好的注释
const count = 0 // 定义count变量

// ✅ 好的注释
const retryCount = 0 // 请求失败重试次数，最大3次

// TODO: 优化用户权限检查逻辑，考虑缓存机制
// FIXME: 修复用户登录状态异常问题
// NOTE: 此处使用防抖处理，避免频繁请求
```

## 项目结构

```
src/
├── api/                    # API接口层
├── assets/                 # 静态资源
├── components/             # 公共组件
├── composables/            # 组合式函数
├── constants/              # 常量定义
├── layouts/                # 页面布局
├── router/                 # 路由配置
├── stores/                 # 状态管理
├── types/                  # 类型定义
├── utils/                  # 工具函数
├── views/                  # 页面组件
├── App.vue
└── main.ts
```

## 组件开发规范

### 组件结构顺序
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script setup lang="ts">
// 1. 框架导入
import { ref, computed } from 'vue'

// 2. 第三方库导入
import { ElMessage } from 'element-plus'

// 3. 项目内导入
import { userApi } from '@/api'

// 4. 类型定义
interface Props {
  id: number
  name?: string
}

// 5. Props/Emits定义
const props = withDefaults(defineProps<Props>(), {
  name: '默认名称'
})

const emit = defineEmits<{
  change: [value: string]
}>()

// 6. 响应式状态
const loading = ref(false)

// 7. 计算属性
const displayName = computed(() => props.name)

// 8. 方法定义
const handleClick = () => {
  emit('change', 'new-value')
}
</script>

<style scoped lang="scss">
.component-name {
  // 样式代码
}
</style>
```

### 命名规范
- **组件名**: PascalCase (UserProfile.vue)
- **Props**: camelCase (userName)
- **Events**: kebab-case (user-update)
- **CSS类**: BEM命名 (user-profile__header)

## TypeScript 使用规范

### 接口定义
```typescript
// types/user.ts
export interface User {
  id: number
  name: string
  email: string
  role: UserRole
  createdAt: string
}

export type UserRole = 'admin' | 'user' | 'guest'

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}
```

## Pinia 状态管理

```typescript
// stores/auth.ts
export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref('')
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  
  // 方法
  const login = async (form: LoginForm) => {
    const response = await authApi.login(form)
    token.value = response.token
    user.value = response.user
    return response
  }
  
  const logout = () => {
    user.value = null
    token.value = ''
  }
  
  return { user, token, isLoggedIn, login, logout }
})
```

## API 层设计

### 请求封装
```typescript
// utils/request.ts
import axios from 'axios'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
request.interceptors.response.use(
  response => response.data.data,
  error => Promise.reject(error)
)

export default request
```

### API模块
```typescript
// api/modules/user.ts
export const userApi = {
  getList: (params: UserQuery) => 
    request.get<User[]>('/users', { params }),
    
  getById: (id: number) => 
    request.get<User>(`/users/${id}`),
    
  create: (data: CreateUserRequest) => 
    request.post<User>('/users', data),
    
  update: (id: number, data: Partial<User>) => 
    request.put<User>(`/users/${id}`, data),
    
  delete: (id: number) => 
    request.delete(`/users/${id}`)
}
```

## 路由配置

### 路由定义
```typescript
// router/modules/user.ts
export const userRoutes = [
  {
    path: '/users',
    name: 'UserManagement',
    component: () => import('@/layouts/BasicLayout.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      permissions: ['user:read']
    },
    children: [
      {
        path: '',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue')
      }
    ]
  }
]
```

### 路由守卫
```typescript
// router/guards/auth.ts
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next('/login')
    return
  }
  
  if (to.meta.permissions) {
    const hasPermission = to.meta.permissions.some(
      permission => authStore.hasPermission(permission)
    )
    if (!hasPermission) {
      next('/403')
      return
    }
  }
  
  next()
})
```

## 样式规范

### SCSS变量
```scss
// assets/styles/variables.scss
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
```

### BEM命名规范
```scss
.user-card {
  &__header {
    padding: $spacing-md;
  }
  
  &__content {
    padding: $spacing-sm;
  }
  
  &--loading {
    opacity: 0.6;
  }
}
```

## 工具配置

### ESLint配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/typescript/recommended'
  ],
  rules: {
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    '@typescript-eslint/no-unused-vars': 'error',
    'no-console': 'warn'
  }
}
```

### Prettier配置
```javascript
// .prettierrc.js
module.exports = {
  printWidth: 80,
  tabWidth: 2,
  semi: false,
  singleQuote: true,
  trailingComma: 'none'
}
```

## 性能优化

### 代码分割
```typescript
// 路由懒加载
const UserList = () => import('@/views/user/UserList.vue')

// 组件懒加载
const HeavyComponent = defineAsyncComponent(
  () => import('@/components/HeavyComponent.vue')
)
```

### 图片优化
```vue
<template>
  <picture>
    <source :srcset="webpSrc" type="image/webp">
    <img :src="jpegSrc" :alt="alt" loading="lazy">
  </picture>
</template>
```

## 错误处理

### 全局错误处理
```typescript
// main.ts
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err)
  ElMessage.error('应用发生错误')
}

window.addEventListener('unhandledrejection', event => {
  console.error('Promise Error:', event.reason)
  ElMessage.error('请求失败')
})
```

## 测试规范

### 组件测试
```typescript
// tests/UserCard.spec.ts
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  it('renders correctly', () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    expect(wrapper.text()).toContain('John Doe')
  })
})
```

## Git 提交规范

### 提交格式
```bash
feat(auth): 添加用户登录功能
fix(api): 修复用户列表分页问题
docs(readme): 更新文档
style(button): 调整按钮样式
refactor(utils): 重构工具函数
perf(list): 优化列表性能
test(user): 添加用户测试
chore(deps): 更新依赖
```

### 代码审查清单
- [ ] 代码符合ESLint规范
- [ ] TypeScript类型完整
- [ ] 组件命名规范
- [ ] 错误处理完善
- [ ] 性能考虑
- [ ] 测试覆盖
- [ ] 文档更新

## 部署配置

### 环境变量
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_ENABLE_MOCK=true

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_ENABLE_MOCK=false
```

### Vite配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: { '@': resolve(__dirname, 'src') }
  },
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js'
      }
    }
  }
})
```

---

**版本**: v2.0.0  
**更新**: 2024年12月

> 此规范基于Vue3生态最佳实践，参考cursor.directory社区规范制定。 