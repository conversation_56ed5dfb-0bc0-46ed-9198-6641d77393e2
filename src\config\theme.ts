/**
 * 主题配置文件
 * @description 定义应用的主题系统，支持明暗主题切换和自定义主题
 * <AUTHOR>
 * @date 2024-12-12
 * @version 1.0.0
 */

export interface ThemeColors {
  // 主色调
  primary: string
  primaryLight: string
  primaryDark: string

  // 辅助色
  secondary: string
  secondaryLight: string
  secondaryDark: string

  // 状态色
  success: string
  warning: string
  error: string
  info: string

  // 背景色
  background: string
  backgroundSecondary: string
  backgroundTertiary: string

  // 文字色
  textPrimary: string
  textSecondary: string
  textTertiary: string
  textDisabled: string

  // 边框色
  border: string
  borderLight: string
  borderDark: string

  // 阴影色
  shadow: string
  shadowLight: string
  shadowDark: string
}

export interface Theme {
  name: string
  displayName: string
  colors: ThemeColors
  isDark: boolean
}

// 浅色主题
export const lightTheme: Theme = {
  name: 'light',
  displayName: '浅色主题',
  isDark: false,
  colors: {
    // 主色调
    primary: '#409eff',
    primaryLight: '#79bbff',
    primaryDark: '#337ecc',

    // 辅助色
    secondary: '#909399',
    secondaryLight: '#b6bbc8',
    secondaryDark: '#73767a',

    // 状态色
    success: '#67c23a',
    warning: '#e6a23c',
    error: '#f56c6c',
    info: '#909399',

    // 背景色
    background: '#ffffff',
    backgroundSecondary: '#f5f7fa',
    backgroundTertiary: '#ebeef5',

    // 文字色
    textPrimary: '#303133',
    textSecondary: '#606266',
    textTertiary: '#909399',
    textDisabled: '#c0c4cc',

    // 边框色
    border: '#dcdfe6',
    borderLight: '#e4e7ed',
    borderDark: '#d3d4d6',

    // 阴影色
    shadow: 'rgba(0, 0, 0, 0.12)',
    shadowLight: 'rgba(0, 0, 0, 0.04)',
    shadowDark: 'rgba(0, 0, 0, 0.24)'
  }
}

// 深色主题
export const darkTheme: Theme = {
  name: 'dark',
  displayName: '深色主题',
  isDark: true,
  colors: {
    // 主色调
    primary: '#409eff',
    primaryLight: '#79bbff',
    primaryDark: '#337ecc',

    // 辅助色
    secondary: '#909399',
    secondaryLight: '#b6bbc8',
    secondaryDark: '#73767a',

    // 状态色
    success: '#67c23a',
    warning: '#e6a23c',
    error: '#f56c6c',
    info: '#909399',

    // 背景色
    background: '#1a1a1a',
    backgroundSecondary: '#2d2d2d',
    backgroundTertiary: '#404040',

    // 文字色
    textPrimary: '#e5eaf3',
    textSecondary: '#cfd3dc',
    textTertiary: '#a3a6ad',
    textDisabled: '#6c6e72',

    // 边框色
    border: '#4c4d4f',
    borderLight: '#414243',
    borderDark: '#58585b',

    // 阴影色
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowLight: 'rgba(0, 0, 0, 0.1)',
    shadowDark: 'rgba(0, 0, 0, 0.5)'
  }
}

// 蓝色主题
export const blueTheme: Theme = {
  name: 'blue',
  displayName: '蓝色主题',
  isDark: false,
  colors: {
    // 主色调
    primary: '#1890ff',
    primaryLight: '#40a9ff',
    primaryDark: '#096dd9',

    // 辅助色
    secondary: '#722ed1',
    secondaryLight: '#9254de',
    secondaryDark: '#531dab',

    // 状态色
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff',

    // 背景色
    background: '#f0f2f5',
    backgroundSecondary: '#fafafa',
    backgroundTertiary: '#f5f5f5',

    // 文字色
    textPrimary: '#000000d9',
    textSecondary: '#00000073',
    textTertiary: '#00000040',
    textDisabled: '#00000025',

    // 边框色
    border: '#d9d9d9',
    borderLight: '#f0f0f0',
    borderDark: '#bfbfbf',

    // 阴影色
    shadow: 'rgba(0, 0, 0, 0.15)',
    shadowLight: 'rgba(0, 0, 0, 0.06)',
    shadowDark: 'rgba(0, 0, 0, 0.25)'
  }
}

// 绿色主题
export const greenTheme: Theme = {
  name: 'green',
  displayName: '绿色主题',
  isDark: false,
  colors: {
    // 主色调
    primary: '#52c41a',
    primaryLight: '#73d13d',
    primaryDark: '#389e0d',

    // 辅助色
    secondary: '#13c2c2',
    secondaryLight: '#36cfc9',
    secondaryDark: '#08979c',

    // 状态色
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff',

    // 背景色
    background: '#f6ffed',
    backgroundSecondary: '#f9ffed',
    backgroundTertiary: '#edf7e3',

    // 文字色
    textPrimary: '#135200',
    textSecondary: '#389e0d',
    textTertiary: '#52c41a',
    textDisabled: '#b7eb8f',

    // 边框色
    border: '#b7eb8f',
    borderLight: '#d9f7be',
    borderDark: '#95de64',

    // 阴影色
    shadow: 'rgba(82, 196, 26, 0.15)',
    shadowLight: 'rgba(82, 196, 26, 0.06)',
    shadowDark: 'rgba(82, 196, 26, 0.25)'
  }
}

// 所有可用主题
export const themes: Record<string, Theme> = {
  light: lightTheme,
  dark: darkTheme,
  blue: blueTheme,
  green: greenTheme
}

// 默认主题
export const DEFAULT_THEME = 'light'

// 主题存储键
export const THEME_STORAGE_KEY = 'vue-service-theme'

// 主题变量映射
export const themeVariables = {
  '--color-primary': 'primary',
  '--color-primary-light': 'primaryLight',
  '--color-primary-dark': 'primaryDark',
  '--color-secondary': 'secondary',
  '--color-secondary-light': 'secondaryLight',
  '--color-secondary-dark': 'secondaryDark',
  '--color-success': 'success',
  '--color-warning': 'warning',
  '--color-error': 'error',
  '--color-info': 'info',
  '--color-background': 'background',
  '--color-background-secondary': 'backgroundSecondary',
  '--color-background-tertiary': 'backgroundTertiary',
  '--color-text-primary': 'textPrimary',
  '--color-text-secondary': 'textSecondary',
  '--color-text-tertiary': 'textTertiary',
  '--color-text-disabled': 'textDisabled',
  '--color-border': 'border',
  '--color-border-light': 'borderLight',
  '--color-border-dark': 'borderDark',
  '--color-shadow': 'shadow',
  '--color-shadow-light': 'shadowLight',
  '--color-shadow-dark': 'shadowDark'
} as const
