// 1. Token刷新机制
class TokenManager {
  private refreshTimer: NodeJS.Timeout | null = null
  private isRefreshing = false
  private failedQueue: Array<{ resolve: Function; reject: Function }> = []

  constructor(private authStore: any) {
    this.setupTokenRefresh()
  }

  private setupTokenRefresh() {
    // 在token过期前5分钟自动刷新
    const token = this.authStore.token
    if (token) {
      const payload = this.parseJWT(token)
      const expiresIn = (payload.exp * 1000) - Date.now() - 5 * 60 * 1000
      
      if (expiresIn > 0) {
        this.refreshTimer = setTimeout(() => {
          this.refreshToken()
        }, expiresIn)
      }
    }
  }

  async refreshToken(): Promise<string> {
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject })
      })
    }

    this.isRefreshing = true

    try {
      const refreshToken = localStorage.getItem('refresh_token')
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await authApi.refreshToken(refreshToken)
      const { token: newToken, refreshToken: newRefreshToken } = response.data

      // 更新token
      this.authStore.token = newToken
      localStorage.setItem('token', newToken)
      if (newRefreshToken) {
        localStorage.setItem('refresh_token', newRefreshToken)
      }

      // 处理等待队列
      this.failedQueue.forEach(({ resolve }) => resolve(newToken))
      this.failedQueue = []

      // 设置下次刷新
      this.setupTokenRefresh()

      return newToken
    } catch (error) {
      // 刷新失败，清除认证信息
      this.failedQueue.forEach(({ reject }) => reject(error))
      this.failedQueue = []
      this.authStore.logout()
      throw error
    } finally {
      this.isRefreshing = false
    }
  }

  private parseJWT(token: string) {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      throw new Error('Invalid token format')
    }
  }

  clearRefreshTimer() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }
}

// 2. 多因子认证 (MFA)
class MFAManager {
  async enableMFA(userId: number): Promise<{ qrCode: string; secret: string }> {
    const response = await request.post('/auth/mfa/enable', { userId })
    return response.data
  }

  async verifyMFA(userId: number, code: string, secret: string): Promise<boolean> {
    const response = await request.post('/auth/mfa/verify', {
      userId,
      code,
      secret
    })
    return response.data.verified
  }

  async loginWithMFA(username: string, password: string, mfaCode: string) {
    const response = await request.post('/auth/login-mfa', {
      username,
      password,
      mfaCode
    })
    return response.data
  }

  generateBackupCodes(): string[] {
    const codes: string[] = []
    for (let i = 0; i < 10; i++) {
      codes.push(this.generateRandomCode(8))
    }
    return codes
  }

  private generateRandomCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

// 3. 会话管理优化
class SessionManager {
  private sessionTimeout = 30 * 60 * 1000 // 30分钟
  private warningTimeout = 5 * 60 * 1000 // 5分钟警告
  private activityTimer: NodeJS.Timeout | null = null
  private warningTimer: NodeJS.Timeout | null = null

  constructor(private authStore: any) {
    this.setupActivityTracking()
  }

  private setupActivityTracking() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    
    events.forEach(event => {
      document.addEventListener(event, this.resetActivityTimer.bind(this), true)
    })

    this.resetActivityTimer()
  }

  private resetActivityTimer() {
    this.clearTimers()

    // 设置警告定时器
    this.warningTimer = setTimeout(() => {
      this.showSessionWarning()
    }, this.sessionTimeout - this.warningTimeout)

    // 设置会话超时定时器
    this.activityTimer = setTimeout(() => {
      this.handleSessionTimeout()
    }, this.sessionTimeout)
  }

  private showSessionWarning() {
    ElMessageBox.confirm(
      '您的会话即将过期，是否继续？',
      '会话警告',
      {
        confirmButtonText: '继续',
        cancelButtonText: '退出',
        type: 'warning',
        countdown: this.warningTimeout / 1000
      }
    ).then(() => {
      this.resetActivityTimer()
    }).catch(() => {
      this.handleSessionTimeout()
    })
  }

  private handleSessionTimeout() {
    ElMessage.warning('会话已过期，请重新登录')
    this.authStore.logout()
    router.push('/login')
  }

  private clearTimers() {
    if (this.activityTimer) {
      clearTimeout(this.activityTimer)
      this.activityTimer = null
    }
    if (this.warningTimer) {
      clearTimeout(this.warningTimer)
      this.warningTimer = null
    }
  }

  destroy() {
    this.clearTimers()
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    events.forEach(event => {
      document.removeEventListener(event, this.resetActivityTimer.bind(this), true)
    })
  }
}

// 4. 密码强度验证
class PasswordValidator {
  private rules = {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    forbiddenPatterns: [
      /(.)\1{2,}/, // 连续相同字符
      /123456|654321|qwerty|password/i, // 常见密码
      /^[0-9]+$/, // 纯数字
      /^[a-zA-Z]+$/ // 纯字母
    ]
  }

  validate(password: string, username?: string): {
    isValid: boolean
    score: number
    errors: string[]
    suggestions: string[]
  } {
    const errors: string[] = []
    const suggestions: string[] = []
    let score = 0

    // 长度检查
    if (password.length < this.rules.minLength) {
      errors.push(`密码长度至少${this.rules.minLength}位`)
    } else if (password.length >= this.rules.minLength) {
      score += 20
    }

    if (password.length > this.rules.maxLength) {
      errors.push(`密码长度不能超过${this.rules.maxLength}位`)
    }

    // 字符类型检查
    if (this.rules.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母')
      suggestions.push('添加大写字母')
    } else if (/[A-Z]/.test(password)) {
      score += 15
    }

    if (this.rules.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母')
      suggestions.push('添加小写字母')
    } else if (/[a-z]/.test(password)) {
      score += 15
    }

    if (this.rules.requireNumbers && !/[0-9]/.test(password)) {
      errors.push('密码必须包含数字')
      suggestions.push('添加数字')
    } else if (/[0-9]/.test(password)) {
      score += 15
    }

    if (this.rules.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符')
      suggestions.push('添加特殊字符 (!@#$%^&*)')
    } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 15
    }

    // 禁用模式检查
    for (const pattern of this.rules.forbiddenPatterns) {
      if (pattern.test(password)) {
        errors.push('密码包含不安全的模式')
        suggestions.push('避免使用连续字符或常见密码')
        break
      }
    }

    // 用户名检查
    if (username && password.toLowerCase().includes(username.toLowerCase())) {
      errors.push('密码不能包含用户名')
      suggestions.push('使用与用户名无关的密码')
    }

    // 复杂度加分
    const uniqueChars = new Set(password).size
    if (uniqueChars >= password.length * 0.7) {
      score += 20 // 字符多样性
    }

    return {
      isValid: errors.length === 0,
      score: Math.min(score, 100),
      errors,
      suggestions
    }
  }

  getStrengthLevel(score: number): 'weak' | 'fair' | 'good' | 'strong' {
    if (score < 40) return 'weak'
    if (score < 60) return 'fair'
    if (score < 80) return 'good'
    return 'strong'
  }

  generateSecurePassword(length = 12): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const numbers = '0123456789'
    const special = '!@#$%^&*(),.?":{}|<>'
    
    let password = ''
    
    // 确保包含每种字符类型
    password += this.getRandomChar(uppercase)
    password += this.getRandomChar(lowercase)
    password += this.getRandomChar(numbers)
    password += this.getRandomChar(special)
    
    // 填充剩余长度
    const allChars = uppercase + lowercase + numbers + special
    for (let i = 4; i < length; i++) {
      password += this.getRandomChar(allChars)
    }
    
    // 随机打乱
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  private getRandomChar(chars: string): string {
    return chars.charAt(Math.floor(Math.random() * chars.length))
  }
}

// 5. 数据加密工具
class CryptoUtils {
  private key: CryptoKey | null = null

  async generateKey(): Promise<CryptoKey> {
    this.key = await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true,
      ['encrypt', 'decrypt']
    )
    return this.key
  }

  async encrypt(data: string): Promise<{ encrypted: string; iv: string }> {
    if (!this.key) {
      await this.generateKey()
    }

    const encoder = new TextEncoder()
    const iv = crypto.getRandomValues(new Uint8Array(12))
    
    const encrypted = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      this.key!,
      encoder.encode(data)
    )

    return {
      encrypted: this.arrayBufferToBase64(encrypted),
      iv: this.arrayBufferToBase64(iv)
    }
  }

  async decrypt(encryptedData: string, ivString: string): Promise<string> {
    if (!this.key) {
      throw new Error('Encryption key not available')
    }

    const encrypted = this.base64ToArrayBuffer(encryptedData)
    const iv = this.base64ToArrayBuffer(ivString)

    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      this.key,
      encrypted
    )

    const decoder = new TextDecoder()
    return decoder.decode(decrypted)
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64)
    const bytes = new Uint8Array(binary.length)
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i)
    }
    return bytes.buffer
  }

  // 哈希函数
  async hash(data: string): Promise<string> {
    const encoder = new TextEncoder()
    const hashBuffer = await crypto.subtle.digest('SHA-256', encoder.encode(data))
    return this.arrayBufferToBase64(hashBuffer)
  }
}
