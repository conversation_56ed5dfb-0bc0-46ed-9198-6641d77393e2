<!--
  主题选择器组件
  @description 提供主题切换功能的下拉选择器
  <AUTHOR>
  @date 2024-12-12
  @version 1.0.0
-->

<template>
  <div class="theme-selector">
    <!-- 主题切换按钮 -->
    <el-dropdown 
      trigger="click" 
      placement="bottom-end"
      @command="handleThemeChange"
    >
      <el-button 
        :icon="themeIcon" 
        circle 
        size="large"
        class="theme-toggle-btn"
        :title="themeLabel"
      >
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu class="theme-dropdown">
          <!-- 预设主题 -->
          <div class="theme-section">
            <div class="theme-section-title">预设主题</div>
            <el-dropdown-item
              v-for="(theme, key) in presetThemes"
              :key="key"
              :command="key"
              :class="{ 'is-active': currentTheme === key }"
              class="theme-item"
            >
              <div class="theme-preview">
                <div 
                  class="theme-color-preview"
                  :style="{ backgroundColor: theme.colors.primary }"
                ></div>
                <div 
                  class="theme-bg-preview"
                  :style="{ backgroundColor: theme.colors.background }"
                ></div>
              </div>
              <span class="theme-name">{{ theme.displayName }}</span>
              <el-icon v-if="currentTheme === key" class="theme-check">
                <Check />
              </el-icon>
            </el-dropdown-item>
          </div>
          
          <!-- 系统主题 -->
          <el-divider style="margin: 8px 0;" />
          <div class="theme-section">
            <el-dropdown-item
              command="auto"
              :class="{ 'is-active': currentTheme === 'auto' }"
              class="theme-item"
            >
              <div class="theme-preview">
                <el-icon><Monitor /></el-icon>
              </div>
              <span class="theme-name">跟随系统</span>
              <el-icon v-if="currentTheme === 'auto'" class="theme-check">
                <Check />
              </el-icon>
            </el-dropdown-item>
          </div>
          
          <!-- 自定义主题 -->
          <template v-if="Object.keys(customThemes).length > 0">
            <el-divider style="margin: 8px 0;" />
            <div class="theme-section">
              <div class="theme-section-title">自定义主题</div>
              <el-dropdown-item
                v-for="(theme, key) in customThemes"
                :key="key"
                :command="key"
                :class="{ 'is-active': currentTheme === key }"
                class="theme-item custom-theme-item"
              >
                <div class="theme-preview">
                  <div 
                    class="theme-color-preview"
                    :style="{ backgroundColor: theme.colors.primary }"
                  ></div>
                  <div 
                    class="theme-bg-preview"
                    :style="{ backgroundColor: theme.colors.background }"
                  ></div>
                </div>
                <span class="theme-name">{{ theme.displayName }}</span>
                <div class="theme-actions">
                  <el-icon v-if="currentTheme === key" class="theme-check">
                    <Check />
                  </el-icon>
                  <el-button
                    size="small"
                    type="danger"
                    :icon="Delete"
                    circle
                    @click.stop="removeTheme(key)"
                    class="delete-theme-btn"
                  />
                </div>
              </el-dropdown-item>
            </div>
          </template>
          
          <!-- 主题管理 -->
          <el-divider style="margin: 8px 0;" />
          <div class="theme-section">
            <el-dropdown-item class="theme-action-item" disabled>
              <el-button 
                size="small" 
                type="primary" 
                :icon="Plus"
                @click="showThemeCreator = true"
              >
                创建主题
              </el-button>
            </el-dropdown-item>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 主题创建器对话框 -->
    <ThemeCreator 
      v-model="showThemeCreator"
      @theme-created="handleThemeCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useTheme, type ThemeType } from '@/composables/useTheme'
import { ElMessage } from 'element-plus'
import { 
  Check, 
  Monitor, 
  Delete, 
  Plus,
  Sunny,
  Moon,
  Water,
  Leaf,
  Palette
} from '@element-plus/icons-vue'
import ThemeCreator from './ThemeCreator.vue'

// 使用主题管理
const {
  currentTheme,
  availableThemes,
  setTheme,
  removeCustomTheme,
  themeLabel,
  themeIcon: themeIconName
} = useTheme()

// 主题创建器显示状态
const showThemeCreator = ref(false)

// 预设主题（排除auto）
const presetThemes = computed(() => {
  const { auto, ...preset } = availableThemes.value
  return Object.fromEntries(
    Object.entries(preset).filter(([key]) => 
      ['light', 'dark', 'blue', 'green'].includes(key)
    )
  )
})

// 自定义主题
const customThemes = computed(() => {
  return Object.fromEntries(
    Object.entries(availableThemes.value).filter(([key]) => 
      !['light', 'dark', 'blue', 'green', 'auto'].includes(key)
    )
  )
})

// 主题图标映射
const iconMap = {
  Sunny,
  Moon,
  Water,
  Leaf,
  Monitor,
  Palette
}

// 当前主题图标
const themeIcon = computed(() => {
  return iconMap[themeIconName.value as keyof typeof iconMap] || Palette
})

/**
 * 处理主题切换
 * @param themeName 主题名称
 */
const handleThemeChange = (themeName: string) => {
  setTheme(themeName as ThemeType)
  ElMessage.success(`已切换到${availableThemes.value[themeName]?.displayName || themeName}`)
}

/**
 * 移除自定义主题
 * @param themeName 主题名称
 */
const removeTheme = (themeName: string) => {
  ElMessageBox.confirm(
    `确定要删除主题"${availableThemes.value[themeName]?.displayName}"吗？`,
    '删除主题',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    removeCustomTheme(themeName)
    ElMessage.success('主题已删除')
  }).catch(() => {
    // 用户取消删除
  })
}

/**
 * 处理主题创建完成
 * @param themeName 新创建的主题名称
 */
const handleThemeCreated = (themeName: string) => {
  ElMessage.success(`主题"${themeName}"创建成功`)
  setTheme(themeName as ThemeType)
}
</script>

<style scoped>
.theme-selector {
  display: inline-block;
}

.theme-toggle-btn {
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text-primary);
}

.theme-toggle-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: scale(1.05);
}

.theme-dropdown {
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;
}

.theme-section {
  padding: 4px 0;
}

.theme-section-title {
  padding: 8px 16px 4px;
  font-size: 12px;
  color: var(--color-text-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.theme-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-item:hover {
  background-color: var(--color-background-secondary);
}

.theme-item.is-active {
  background-color: var(--color-primary);
  color: white;
}

.theme-preview {
  display: flex;
  align-items: center;
  margin-right: 12px;
  width: 24px;
  height: 24px;
}

.theme-color-preview {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 4px;
  border: 1px solid var(--color-border);
}

.theme-bg-preview {
  width: 8px;
  height: 8px;
  border-radius: 2px;
  border: 1px solid var(--color-border);
}

.theme-name {
  flex: 1;
  font-size: 14px;
}

.theme-check {
  margin-left: 8px;
  color: var(--color-success);
}

.theme-item.is-active .theme-check {
  color: white;
}

.custom-theme-item .theme-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.delete-theme-btn {
  width: 20px;
  height: 20px;
  padding: 0;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.delete-theme-btn:hover {
  opacity: 1;
}

.theme-action-item {
  padding: 8px 16px;
  cursor: default;
}

.theme-action-item:hover {
  background: none;
}

/* 滚动条样式 */
.theme-dropdown::-webkit-scrollbar {
  width: 4px;
}

.theme-dropdown::-webkit-scrollbar-track {
  background: var(--color-background-tertiary);
}

.theme-dropdown::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 2px;
}

.theme-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-dark);
}
</style>
