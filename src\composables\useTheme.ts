/**
 * 主题管理 Composable
 * @description 管理应用主题状态和切换，支持多种主题和自定义主题
 * <AUTHOR>
 * @date 2024-12-12
 * @version 2.0.0
 */

import { ref, computed, watch, onMounted, readonly } from 'vue'
import {
  themes,
  DEFAULT_THEME,
  THEME_STORAGE_KEY,
  themeVariables,
  type Theme
} from '@/config/theme'

// 主题类型
export type ThemeType = 'light' | 'dark' | 'blue' | 'green' | 'auto'

// 主题状态
const currentTheme = ref<ThemeType>(DEFAULT_THEME as ThemeType)
const systemTheme = ref<'light' | 'dark'>('light')
const customThemes = ref<Record<string, Theme>>({})

/**
 * 主题管理 Hook
 */
export function useTheme() {
  // 获取所有可用主题
  const availableThemes = computed(() => ({
    ...themes,
    ...customThemes.value
  }))

  // 计算实际主题
  const actualTheme = computed(() => {
    if (currentTheme.value === 'auto') {
      return systemTheme.value
    }
    return currentTheme.value
  })

  // 获取当前主题对象
  const currentThemeObject = computed(() => {
    return availableThemes.value[actualTheme.value] || themes.light
  })

  // 是否为深色主题
  const isDark = computed(() => currentThemeObject.value.isDark)

  // 主题颜色
  const themeColors = computed(() => currentThemeObject.value.colors)

  /**
   * 设置主题
   * @param theme 主题类型
   */
  const setTheme = (theme: ThemeType) => {
    currentTheme.value = theme
    localStorage.setItem(THEME_STORAGE_KEY, theme)
    applyTheme()
  }

  /**
   * 切换主题
   */
  const toggleTheme = () => {
    const themeKeys = Object.keys(availableThemes.value).filter(
      key => key !== 'auto'
    ) as ThemeType[]
    themeKeys.push('auto')

    const currentIndex = themeKeys.indexOf(currentTheme.value)
    const nextIndex = (currentIndex + 1) % themeKeys.length
    setTheme(themeKeys[nextIndex])
  }

  /**
   * 应用主题到DOM
   */
  const applyTheme = () => {
    const theme = currentThemeObject.value
    const root = document.documentElement

    // 设置主题属性
    root.setAttribute('data-theme', theme.name)
    root.classList.toggle('dark', theme.isDark)

    // 应用CSS变量
    Object.entries(themeVariables).forEach(([cssVar, colorKey]) => {
      const colorValue = theme.colors[colorKey as keyof typeof theme.colors]
      root.style.setProperty(cssVar, colorValue)
    })

    // 更新Element Plus主题
    updateElementPlusTheme(theme)
  }

  /**
   * 更新Element Plus主题
   */
  const updateElementPlusTheme = (theme: Theme) => {
    const root = document.documentElement

    // Element Plus CSS变量映射
    const elementPlusVars = {
      '--el-color-primary': theme.colors.primary,
      '--el-color-success': theme.colors.success,
      '--el-color-warning': theme.colors.warning,
      '--el-color-danger': theme.colors.error,
      '--el-color-info': theme.colors.info,
      '--el-bg-color': theme.colors.background,
      '--el-bg-color-page': theme.colors.backgroundSecondary,
      '--el-text-color-primary': theme.colors.textPrimary,
      '--el-text-color-regular': theme.colors.textSecondary,
      '--el-text-color-secondary': theme.colors.textTertiary,
      '--el-text-color-placeholder': theme.colors.textDisabled,
      '--el-border-color': theme.colors.border,
      '--el-border-color-light': theme.colors.borderLight,
      '--el-border-color-lighter': theme.colors.borderLight,
      '--el-border-color-extra-light': theme.colors.borderLight
    }

    Object.entries(elementPlusVars).forEach(([cssVar, value]) => {
      root.style.setProperty(cssVar, value)
    })
  }

  /**
   * 添加自定义主题
   * @param theme 自定义主题
   */
  const addCustomTheme = (theme: Theme) => {
    customThemes.value[theme.name] = theme

    // 保存到本地存储
    const savedCustomThemes = JSON.parse(localStorage.getItem('custom-themes') || '{}')
    savedCustomThemes[theme.name] = theme
    localStorage.setItem('custom-themes', JSON.stringify(savedCustomThemes))
  }

  /**
   * 移除自定义主题
   * @param themeName 主题名称
   */
  const removeCustomTheme = (themeName: string) => {
    delete customThemes.value[themeName]

    // 从本地存储移除
    const savedCustomThemes = JSON.parse(localStorage.getItem('custom-themes') || '{}')
    delete savedCustomThemes[themeName]
    localStorage.setItem('custom-themes', JSON.stringify(savedCustomThemes))

    // 如果当前使用的是被删除的主题，切换到默认主题
    if (currentTheme.value === themeName) {
      setTheme(DEFAULT_THEME as ThemeType)
    }
  }

  /**
   * 检测系统主题
   */
  const detectSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      systemTheme.value = mediaQuery.matches ? 'dark' : 'light'

      // 监听系统主题变化
      mediaQuery.addEventListener('change', e => {
        systemTheme.value = e.matches ? 'dark' : 'light'
      })
    }
  }

  /**
   * 初始化主题
   */
  const initTheme = () => {
    // 加载自定义主题
    const savedCustomThemes = JSON.parse(localStorage.getItem('custom-themes') || '{}')
    customThemes.value = savedCustomThemes

    // 从本地存储读取主题设置
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) as ThemeType
    if (
      savedTheme &&
      (Object.keys(availableThemes.value).includes(savedTheme) || savedTheme === 'auto')
    ) {
      currentTheme.value = savedTheme
    }

    // 检测系统主题
    detectSystemTheme()

    // 应用主题
    applyTheme()
  }

  /**
   * 获取主题预览色彩
   * @param themeName 主题名称
   */
  const getThemePreview = (themeName: string) => {
    const theme = availableThemes.value[themeName]
    if (!theme) return null

    return {
      primary: theme.colors.primary,
      background: theme.colors.background,
      text: theme.colors.textPrimary
    }
  }

  /**
   * 主题显示名称
   */
  const themeLabel = computed(() => {
    const labels: Record<ThemeType, string> = {
      light: '浅色模式',
      dark: '深色模式',
      blue: '蓝色主题',
      green: '绿色主题',
      auto: '跟随系统'
    }
    return labels[currentTheme.value] || currentThemeObject.value.displayName
  })

  /**
   * 主题图标名称
   */
  const themeIcon = computed(() => {
    const icons: Record<ThemeType, string> = {
      light: 'Sunny',
      dark: 'Moon',
      blue: 'Water',
      green: 'Leaf',
      auto: 'Monitor'
    }
    return icons[currentTheme.value] || 'Palette'
  })

  // 监听主题变化
  watch(actualTheme, applyTheme)
  watch(systemTheme, () => {
    if (currentTheme.value === 'auto') {
      applyTheme()
    }
  })

  // 组件挂载时初始化
  onMounted(initTheme)

  return {
    // 状态
    currentTheme: readonly(currentTheme),
    actualTheme,
    currentThemeObject,
    availableThemes,
    isDark,
    themeColors,

    // 计算属性
    themeLabel,
    themeIcon,

    // 方法
    setTheme,
    toggleTheme,
    addCustomTheme,
    removeCustomTheme,
    getThemePreview,
    initTheme
  }
}
